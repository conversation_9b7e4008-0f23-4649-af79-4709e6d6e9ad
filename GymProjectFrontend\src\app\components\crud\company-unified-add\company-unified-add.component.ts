import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { CompanyService } from '../../../services/company.service';
import { CompanyadressService } from '../../../services/companyadress.service';
import { CompanyUserService } from '../../../services/company-user.service';
import { UserCompanyService } from '../../../services/usercompany.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';
import { AuthService } from '../../../services/auth.service';
import { UserService } from '../../../services/user-service.service';
import { City } from '../../../models/city';
import { Town } from '../../../models/town';
import { Company } from '../../../models/company';
import { CompanyAdress } from '../../../models/companyAdress';
import { CompanyUser } from '../../../models/companyUser';
import { UserCompany } from '../../../models/usercompany';
import { CompanyAdressDetailDto } from '../../../models/companyAdressDetailDto';
import { CompanyUserDetail } from '../../../models/companyUserDetails';
import { UserCompanyDetail } from '../../../models/userCompanyDetailDto';
import { User } from '../../../models/user';
import { CompanyWithOwnerDto } from '../../../models/company-with-owner-dto';
import { faTrashAlt, faEdit, faArrowLeft, faArrowRight, faCheck, faInfoCircle, faBuilding, faMapMarkedAlt, faUserTie, faEye, faUser, faQuestion, faMapMarkerAlt } from '@fortawesome/free-solid-svg-icons';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { MatDialog } from '@angular/material/dialog';

// Wizard adımları enum'u
enum WizardStep {
  USER_REGISTRATION = 1,
  COMPANY_INFO = 2,
  ADDRESS_INFO = 3,
  OWNER_INFO = 4,
  PREVIEW = 5
}

@Component({
  selector: 'app-company-unified-add',
  templateUrl: './company-unified-add.component.html',
  styleUrls: ['./company-unified-add.component.css'],
  standalone: false
})
export class CompanyUnifiedAddComponent implements OnInit {
  // Icons
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faBuilding = faBuilding;
  faMapMarkedAlt = faMapMarkedAlt;
  faUserTie = faUserTie;
  faEye = faEye;
  faUser = faUser;
  faQuestion = faQuestion;
  faMapMarkerAlt = faMapMarkerAlt;

  // Wizard Step enum'unu template'te kullanabilmek için
  WizardStep = WizardStep;

  // Wizard state
  currentStep: WizardStep = WizardStep.USER_REGISTRATION;
  completedSteps: Set<WizardStep> = new Set();

  // Forms
  userRegistrationForm: FormGroup;
  companyInfoForm: FormGroup;
  addressInfoForm: FormGroup;
  ownerInfoForm: FormGroup;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  filteredTowns: Town[] = [];
  salons: any[] = []; // Birleştirilmiş salon listesi
  filteredSalons: any[] = []; // Filtrelenmiş salon listesi
  isSubmitting: boolean = false;
  searchTerm: string = '';
  sortColumn: string = 'companyName';
  sortDirection: string = 'asc';

  // Wizard data
  wizardData: any = {};

  constructor(
    private formBuilder: FormBuilder,
    private companyService: CompanyService,
    private companyAdressService: CompanyadressService,
    private companyUserService: CompanyUserService,
    private userCompanyService: UserCompanyService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private dialog: MatDialog,
    private authService: AuthService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.getCities();
    this.getTowns();
    this.getSalons();
  }

  initializeForms(): void {
    // Adım 1: Kullanıcı kaydı formu
    this.userRegistrationForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]]
    });

    // Adım 2: Şirket bilgileri formu
    this.companyInfoForm = this.formBuilder.group({
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });

    // Adım 3: Adres bilgileri formu
    this.addressInfoForm = this.formBuilder.group({
      cityID: ['', Validators.required],
      townID: ['', Validators.required],
      address: ['', [Validators.required, Validators.minLength(10)]]
    });

    // Adım 4: Salon sahibi bilgileri formu
    this.ownerInfoForm = this.formBuilder.group({
      ownerName: ['', [Validators.required, Validators.minLength(2)]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      // Mevcut adımı tamamlandı olarak işaretle
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.USER_REGISTRATION) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  canGoToStep(step: WizardStep): boolean {
    // Mevcut adıma her zaman gidebilir
    if (step === this.currentStep) return true;

    // Tamamlanmış adımlara gidebilir
    if (this.completedSteps.has(step)) return true;

    // Bir sonraki adıma gidebilir mi kontrol et
    const previousStep = step - 1;
    if (previousStep >= WizardStep.USER_REGISTRATION) {
      return this.completedSteps.has(previousStep);
    }

    // İlk adıma her zaman gidebilir
    return step === WizardStep.USER_REGISTRATION;
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        this.saveUserRegistrationInfo();
        break;
      case WizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        break;
      case WizardStep.OWNER_INFO:
        this.saveAddressInfo();
        break;
      case WizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  // Step validation methods
  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return this.userRegistrationForm.valid;
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm.valid;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm.valid;
      default:
        return true;
    }
  }

  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  // UI Helper metodları
  getStepButtonClass(step: WizardStep): string {
    if (this.currentStep === step) {
      return 'btn-primary'; // Aktif adım - mavi
    } else if (this.isStepCompleted(step)) {
      return 'btn-outline-success'; // Tamamlanmış adım - yeşil çerçeve
    } else {
      return 'btn-outline-secondary'; // Henüz erişilmemiş adım - gri
    }
  }

  getStepIcon(step: WizardStep): any {
    // Tamamlanmış adımlar için check ikonu
    if (this.isStepCompleted(step)) {
      return this.faCheck;
    }

    // Aktif veya henüz tamamlanmamış adımlar için varsayılan ikon
    switch (step) {
      case WizardStep.USER_REGISTRATION:
        return this.faInfoCircle;
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkedAlt;
      case WizardStep.OWNER_INFO:
        return this.faUserTie;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }

  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return 'Kullanıcı Kaydı';
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi';
      case WizardStep.PREVIEW:
        return 'Önizleme ve Kaydet';
      default:
        return '';
    }
  }

  getProgress(): number {
    const totalSteps = 5;
    const completedStepsCount = this.completedSteps.size;
    const currentStepProgress = this.currentStep === WizardStep.PREVIEW ? 1 : 0;
    return ((completedStepsCount + currentStepProgress) / totalSteps) * 100;
  }

  validateAndHighlightErrors(): void {
    const currentForm = this.getCurrentForm();
    if (currentForm) {
      Object.keys(currentForm.controls).forEach(key => {
        const control = currentForm.get(key);
        if (control && control.invalid) {
          control.markAsTouched();
        }
      });
    }
  }

  getCurrentForm(): FormGroup | null {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return this.userRegistrationForm;
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm;
      default:
        return null;
    }
  }

  // Hata durumunda şirketi temizle
  private cleanupCompany(companyId: number): void {
    this.companyService.delete(companyId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket silindi (ID: ${companyId})`);
      },
      (error) => {
        console.error(`Şirket temizleme hatası (ID: ${companyId}):`, error);
      }
    );
  }

  // Wizard data save methods
  saveUserRegistrationInfo(): void {
    if (this.userRegistrationForm.valid) {
      this.wizardData.firstName = this.userRegistrationForm.get('firstName')?.value;
      this.wizardData.lastName = this.userRegistrationForm.get('lastName')?.value;
      this.wizardData.email = this.userRegistrationForm.get('email')?.value;
    }
  }

  saveCompanyInfo(): void {
    if (this.companyInfoForm.valid) {
      this.wizardData.companyName = this.companyInfoForm.get('companyName')?.value;
      this.wizardData.companyPhone = this.companyInfoForm.get('companyPhone')?.value;
    }
  }

  saveAddressInfo(): void {
    if (this.addressInfoForm.valid) {
      this.wizardData.cityID = this.addressInfoForm.get('cityID')?.value;
      this.wizardData.townID = this.addressInfoForm.get('townID')?.value;
      this.wizardData.address = this.addressInfoForm.get('address')?.value;
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerInfoForm.valid) {
      this.wizardData.ownerName = this.ownerInfoForm.get('ownerName')?.value;
      this.wizardData.ownerPhone = this.ownerInfoForm.get('ownerPhone')?.value;
    }
  }

  preparePreviewData(): void {
    // Tüm form verilerini birleştir
    this.saveUserRegistrationInfo();
    this.saveCompanyInfo();
    this.saveAddressInfo();
    this.saveOwnerInfo();

    // Şehir ve ilçe isimlerini bul
    const selectedCity = this.cities.find(c => c.cityID === this.wizardData.cityID);
    const selectedTown = this.filteredTowns.find(t => t.townID === this.wizardData.townID);

    this.wizardData.cityName = selectedCity?.cityName || '';
    this.wizardData.townName = selectedTown?.townName || '';
  }

  // Şehir değiştiğinde ilçeleri filtrele
  onCityChange(): void {
    const cityID = this.addressInfoForm.get('cityID')?.value;
    if (cityID) {
      this.townService.getTownsByCityId(cityID).subscribe(
        (response) => {
          this.filteredTowns = response.data;
          // İlçe seçimini sıfırla
          this.addressInfoForm.get('townID')?.setValue('');
        },
        (error) => {
          console.error('İlçeler yüklenirken hata:', error);
          this.filteredTowns = [];
        }
      );
    } else {
      this.filteredTowns = [];
      this.addressInfoForm.get('townID')?.setValue('');
    }
  }

  // Hata durumunda şirket adresini temizle
  private cleanupCompanyAddress(addressId: number): void {
    this.companyAdressService.delete(addressId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket adresi silindi (ID: ${addressId})`);
      },
      (error) => {
        console.error(`Şirket adresi temizleme hatası (ID: ${addressId}):`, error);
      }
    );
  }

  // Hata durumunda şirket sahibini temizle
  private cleanupCompanyUser(ownerId: number): void {
    this.companyUserService.delete(ownerId).subscribe(
      () => {
        console.log(`Hata nedeniyle şirket sahibi silindi (ID: ${ownerId})`);
      },
      (error) => {
        console.error(`Şirket sahibi temizleme hatası (ID: ${ownerId}):`, error);
      }
    );
  }

  // Wizard final save method
  saveCompanyWithOwner(): void {
    if (!this.validateAllForms()) {
      this.toastrService.error('Lütfen tüm alanları eksiksiz doldurunuz', 'Hata');
      return;
    }

    this.isSubmitting = true;
    this.preparePreviewData();

    // CompanyWithOwnerDto oluştur
    const companyWithOwnerDto = {
      firstName: this.wizardData.firstName,
      lastName: this.wizardData.lastName,
      email: this.wizardData.email,
      companyName: this.wizardData.companyName,
      companyPhone: this.wizardData.companyPhone,
      cityID: this.wizardData.cityID,
      townID: this.wizardData.townID,
      address: this.wizardData.address,
      ownerName: this.wizardData.ownerName,
      ownerPhone: this.wizardData.ownerPhone
    };

    this.companyService.addCompanyWithOwner(companyWithOwnerDto).subscribe(
      (response: any) => {
        this.toastrService.success('Salon başarıyla oluşturuldu', 'Başarılı');
        this.resetWizard();
        this.getSalons();
        this.isSubmitting = false;
      },
      (error: any) => {
        this.toastrService.error('Salon oluşturulurken hata oluştu: ' + (error.error?.message || error.message), 'Hata');
        console.error('Salon oluşturma hatası:', error);
        this.isSubmitting = false;
      }
    );
  }

  // Tüm formları doğrula
  validateAllForms(): boolean {
    return this.userRegistrationForm.valid &&
           this.companyInfoForm.valid &&
           this.addressInfoForm.valid &&
           this.ownerInfoForm.valid;
  }

  resetWizard(): void {
    // Wizard state'i sıfırla
    this.currentStep = WizardStep.USER_REGISTRATION;
    this.completedSteps.clear();
    this.wizardData = {};

    // Formları sıfırla
    this.userRegistrationForm.reset();
    this.companyInfoForm.reset();
    this.addressInfoForm.reset();
    this.ownerInfoForm.reset();

    // Filtrelenmiş ilçeleri temizle
    this.filteredTowns = [];
  }

  getSalons() {
    this.isSubmitting = true;
    
    // Şirket-şirket sahibi ilişkilerini al
    this.userCompanyService.getUserCompanyDetails().subscribe(
      (response) => {
        // UserCompanyDetail modelinde companyId ve userId alanları yok, bu yüzden kendi özel salon modelimizi oluşturuyoruz
        this.salons = [];
        
        // Her bir şirket için salon bilgisi oluştur
        response.data.forEach(uc => {
          // Salon bilgisini oluştur
          const salon = {
            companyName: uc.companyName,
            ownerName: uc.companyUserName,
            userCompanyId: uc.userCompanyId,
            // Diğer bilgiler daha sonra doldurulacak
          };
          
          this.salons.push(salon);
        });
        
        // Şirket adreslerini al ve salon bilgilerine ekle
        this.companyAdressService.getCompanyAdressesDetails().subscribe(
          (addressResponse) => {
            const addresses = addressResponse.data;
            
            this.salons.forEach(salon => {
              // CompanyAdressDetailDto'da companyID alanı olmadığı için companyName ile eşleştiriyoruz
              const address = addresses.find(a => a.companyName === salon.companyName);
              if (address) {
                salon.cityName = address.cityName;
                salon.townName = address.townName;
                salon.address = address.adress;
                salon.companyAdressID = address.companyAdressID;
              }
            });
            
            // Şirketleri al ve salon bilgilerine ekle
            this.companyService.getCompanies().subscribe(
              (companiesResponse) => {
                const companies = companiesResponse.data;
                
                this.salons.forEach(salon => {
                  const company = companies.find(c => c.companyName === salon.companyName);
                  if (company) {
                    salon.companyId = company.companyID;
                  }
                });
                
                // Şirket sahiplerinin detaylarını al ve salon bilgilerine ekle
                this.companyUserService.getCompanyUserDetails().subscribe(
                  (ownerResponse) => {
                    const owners = ownerResponse.data;
                    
                    this.salons.forEach(salon => {
                      const owner = owners.find(o => o.companyUserName === salon.ownerName);
                      if (owner) {
                        salon.ownerId = owner.companyUserId;
                        salon.ownerPhone = owner.companyUserPhoneNumber;
                        salon.ownerEmail = owner.companyUserEmail;
                      }
                    });
                    
                    // Filtrelenmiş salonları başlat
                    this.filteredSalons = [...this.salons];
                    // Varsayılan sıralama uygula
                    this.sortSalons(this.sortColumn);
                    
                    this.isSubmitting = false;
                  },
                  (error) => {
                    console.error('Şirket sahipleri yükleme hatası:', error);
                    this.isSubmitting = false;
                  }
                );
              },
              (error) => {
                console.error('Şirketler yükleme hatası:', error);
                this.isSubmitting = false;
              }
            );
          },
          (error) => {
            console.error('Şirket adresleri yükleme hatası:', error);
            this.isSubmitting = false;
          }
        );
      },
      (error) => {
        console.error('Şirket-şirket sahibi ilişkileri yükleme hatası:', error);
        this.isSubmitting = false;
      }
    );
  }

  // Salonları filtrele
  filterSalons(): void {
    if (!this.searchTerm.trim()) {
      this.filteredSalons = [...this.salons];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredSalons = this.salons.filter(salon => 
        salon.companyName?.toLowerCase().includes(term) || 
        salon.ownerName?.toLowerCase().includes(term) || 
        salon.cityName?.toLowerCase().includes(term) || 
        salon.townName?.toLowerCase().includes(term) ||
        salon.ownerPhone?.toLowerCase().includes(term) ||
        salon.ownerEmail?.toLowerCase().includes(term)
      );
    }
    // Mevcut sıralamayı koru
    this.sortSalons(this.sortColumn);
  }

  // Salonları sırala
  sortSalons(column: string): void {
    this.sortColumn = column;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    
    this.filteredSalons.sort((a: any, b: any) => {
      let aValue = a[column];
      let bValue = b[column];
      
      // Null veya undefined değerleri kontrol et
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';
      
      // String değerleri küçük harfe çevir
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  // Benzersiz şehir sayısını al
  getUniqueCityCount(): number {
    if (this.salons.length === 0) return 0;
    
    const uniqueCities = new Set(this.salons
      .filter(salon => salon.cityName) // undefined veya null olanları filtrele
      .map(salon => salon.cityName));
    
    return uniqueCities.size;
  }

  getCities() {
    this.cityService.getCities().subscribe((response) => {
      this.cities = response.data;
    });
  }

  getTowns() {
    this.townService.getTowns().subscribe((response) => {
      this.towns = response.data;
    });
  }

  // Progress calculation
  getProgress(): number {
    const totalSteps = 5;
    const completedStepsCount = this.completedSteps.size;
    const currentStepProgress = this.currentStep <= totalSteps ? 1 : 0;
    return Math.min(((completedStepsCount + currentStepProgress) / totalSteps) * 100, 100);
  }

  // Step title getter
  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return 'Kullanıcı Kaydı';
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW:
        return 'Önizleme ve Kaydet';
      default:
        return 'Bilinmeyen Adım';
    }
  }

  // Step button class getter
  getStepButtonClass(step: WizardStep): string {
    if (this.currentStep === step) {
      return 'btn-step active';
    } else if (this.completedSteps.has(step)) {
      return 'btn-step completed';
    } else {
      return 'btn-step';
    }
  }

  // Step icon getter
  getStepIcon(step: WizardStep): any {
    switch (step) {
      case WizardStep.USER_REGISTRATION:
        return this.faUser;
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkerAlt;
      case WizardStep.OWNER_INFO:
        return this.faUserTie;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faQuestion;
    }
  }

  // City change handler
  onCityChange(): void {
    const cityID = this.addressInfoForm.get('cityID')?.value;
    if (cityID) {
      this.filteredTowns = this.towns.filter(town => town.cityID === cityID);
      this.addressInfoForm.get('townID')?.setValue(null);
    } else {
      this.filteredTowns = [];
    }
  }

  // Salon listesini getir
  getSalons(): void {
    this.companyadressService.getCompanyAdressesDetails().subscribe(
      (response) => {
        this.salons = response.data;
        this.filteredSalons = [...this.salons];
      },
      (error) => {
        console.error('Salon listesi yüklenirken hata:', error);
        this.toastrService.error('Salon listesi yüklenemedi', 'Hata');
      }
    );
  }

  // Salon filtreleme
  filterSalons(): void {
    if (!this.searchTerm) {
      this.filteredSalons = [...this.salons];
    } else {
      const term = this.searchTerm.toLowerCase();
      this.filteredSalons = this.salons.filter(salon =>
        salon.companyName.toLowerCase().includes(term) ||
        salon.ownerName.toLowerCase().includes(term) ||
        salon.cityName.toLowerCase().includes(term) ||
        salon.townName.toLowerCase().includes(term)
      );
    }
  }

  // Salon sıralama
  sortSalons(field: string): void {
    this.filteredSalons.sort((a, b) => {
      const aValue = (a as any)[field];
      const bValue = (b as any)[field];
      return aValue.localeCompare(bValue);
    });
  }

  // Benzersiz şehir sayısı
  getUniqueCityCount(): number {
    const uniqueCities = new Set(this.salons.map(salon => salon.cityName));
    return uniqueCities.size;
  }

  // Salon silme
  deleteSalon(salon: any): void {
    if (confirm(`${salon.companyName} adlı salonu silmek istediğinizden emin misiniz?`)) {
      // Burada silme işlemi yapılacak
      this.toastrService.info('Salon silme işlemi henüz implement edilmedi', 'Bilgi');
    }
  }

  // Şehirleri getir
  getCities(): void {
    this.cityService.getCities().subscribe(
      (response) => {
        this.cities = response.data;
      },
      (error) => {
        console.error('Şehirler yüklenirken hata:', error);
        this.toastrService.error('Şehirler yüklenemedi', 'Hata');
      }
    );
  }

  // İlçeleri getir
  getTowns(): void {
    this.townService.getTowns().subscribe(
      (response) => {
        this.towns = response.data;
      },
      (error) => {
        console.error('İlçeler yüklenirken hata:', error);
        this.toastrService.error('İlçeler yüklenemedi', 'Hata');
      }
    );
  }

  // Ensure this method is properly defined
  deleteSalon(salon: any): void {
    if (confirm("Bu salonu silmek istediğinizden emin misiniz? Bu işlem şirket, adres ve şirket sahibi ilişkilerini silecektir.")) {
      this.isSubmitting = true;
      
      // Önce şirket-şirket sahibi ilişkisini sil
      this.userCompanyService.delete(salon.userCompanyId).subscribe(
        () => {
          this.toastrService.success('Şirket-şirket sahibi ilişkisi silindi', 'Başarılı');
          
          // Şirket adresini sil
          if (salon.companyAdressID) {
            this.companyAdressService.delete(salon.companyAdressID).subscribe(
              () => {
                this.toastrService.success('Şirket adresi silindi', 'Başarılı');
                
                // Şirketi sil
                this.companyService.delete(salon.companyId).subscribe(
                  () => {
                    this.toastrService.success('Salon başarıyla silindi', 'Başarılı');
                    this.getSalons();
                  },
                  (error) => {
                    this.toastrService.error('Şirket silinemedi', 'Hata');
                    console.error('Şirket silme hatası:', error);
                    this.isSubmitting = false;
                  }
                );
              },
              (error) => {
                this.toastrService.error('Şirket adresi silinemedi', 'Hata');
                console.error('Şirket adresi silme hatası:', error);
                this.isSubmitting = false;
              }
            );
          } else {
            // Şirket adresi yoksa doğrudan şirketi sil
            this.companyService.delete(salon.companyId).subscribe(
              () => {
                this.toastrService.success('Salon başarıyla silindi', 'Başarılı');
                this.getSalons();
              },
              (error) => {
                this.toastrService.error('Şirket silinemedi', 'Hata');
                console.error('Şirket silme hatası:', error);
                this.isSubmitting = false;
              }
            );
          }
        },
        (error) => {
          this.toastrService.error('Şirket-şirket sahibi ilişkisi silinemedi', 'Hata');
          console.error('İlişki silme hatası:', error);
          this.isSubmitting = false;
        }
      );
    }
  }
}
