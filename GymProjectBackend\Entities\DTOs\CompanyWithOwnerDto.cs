using Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Entities.DTOs
{
    public class CompanyWithOwnerDto : IDto
    {
        // User bilgileri
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        // Password alanı kaldırıldı - telefon numarasının son 4 hanesi otomatik olarak kullanılacak

        // Şirket bilgileri
        public string CompanyName { get; set; }
        public string CompanyPhone { get; set; }

        // Adres bilgileri
        public int CityID { get; set; }
        public int TownID { get; set; }
        public string Address { get; set; }

        // Salon sahibi bilgileri
        public string OwnerName { get; set; }
        public string OwnerPhone { get; set; }
    }
}
