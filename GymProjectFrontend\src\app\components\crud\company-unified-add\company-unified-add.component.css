/* Wizard Styles */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: var(--bg-primary);
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.main-content {
  padding-top: 2rem;
}

.wizard-step {
  min-height: 400px;
  animation: fadeInUp 0.5s ease-out;
}

.progress-container {
  margin-top: 1rem;
}

.progress {
  height: 8px;
  background-color: var(--bg-secondary);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  transition: width 0.3s ease;
}

/* Step Navigation Buttons */
.btn-step {
  border: 1px solid var(--border-color);
  background: var(--bg-primary);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.btn-step:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.btn-step.active {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

/* Preview Section Styles */
.preview-summary {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 2rem;
  border: 1px solid var(--border-color);
}

.preview-section {
  background: var(--bg-primary);
  border-radius: 6px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
}

.preview-section h6 {
  color: var(--text-primary);
  margin-bottom: 1rem;
  font-weight: 600;
}

.preview-section p {
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
}

.preview-section p:last-child {
  margin-bottom: 0;
}

/* Content blur effect during loading */
.content-blur {
  filter: blur(2px);
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Salon icon styling */
.salon-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.btn-step.completed {
  background: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-step:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Preview Section */
.preview-summary {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: 1.5rem;
}

.preview-section {
  background: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid var(--border-color);
}

.preview-section h6 {
  color: var(--primary);
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.preview-section p {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.preview-section p:last-child {
  margin-bottom: 0;
}

/* Blur effect for loading state */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Full width form fields */
mat-form-field {
  width: 100%;
  margin-bottom: 8px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table styling */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: middle;
  border-top: 1px solid var(--border-color);
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.table tbody tr:hover {
  background-color: var(--primary-light);
}

/* Button spacing */
.btn {
  margin-right: 0.5rem;
}

.btn:last-child {
  margin-right: 0;
}

/* Card styling */
.card {
  margin-bottom: 1.5rem;
  border: none;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 1rem 1.25rem;
}

.card-body {
  padding: 1.25rem;
}

/* Responsive table */
@media (max-width: 767.98px) {
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
