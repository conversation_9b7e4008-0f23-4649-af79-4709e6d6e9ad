<!-- <PERSON><PERSON> Header -->
<div class="sticky-header">
  <div class="container-fluid">
    <!-- Header -->
    <div class="modern-card mb-4">
      <div class="modern-card-header">
        <div class="d-flex align-items-center">
          <div>
            <h4 class="mb-0">Yeni Salon Ekleme</h4>
            <small class="text-muted">{{getStepTitle()}} - Adım {{currentStep}} / 5</small>
          </div>
        </div>
        <div class="d-flex gap-2">
          <button
            type="button"
            class="modern-btn modern-btn-secondary"
            (click)="resetWizard()">
            İptal
          </button>
          <button
            *ngIf="currentStep < WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-outline-primary"
            [disabled]="currentStep === WizardStep.USER_REGISTRATION"
            (click)="previousStep()">
            <fa-icon [icon]="faArrowLeft"></fa-icon>
            Önceki
          </button>
          <button
            *ngIf="currentStep < WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-primary"
            [disabled]="!canProceedToNextStep()"
            (click)="nextStep()">
            Sonraki
            <fa-icon [icon]="faArrowRight"></fa-icon>
          </button>
          <button
            *ngIf="currentStep === WizardStep.PREVIEW"
            type="button"
            class="modern-btn modern-btn-success"
            [disabled]="isSubmitting || !validateAllForms()"
            (click)="saveCompanyWithOwner()">
            <fa-icon [icon]="faCheck"></fa-icon>
            {{ isSubmitting ? 'Kaydediliyor...' : 'Kaydet' }}
          </button>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="progress-container">
        <div class="progress">
          <div class="progress-bar bg-primary"
               [style.width.%]="getProgress()"
               role="progressbar">
          </div>
        </div>
        <small class="text-muted mt-1">İlerleme: {{getProgress() | number:'1.0-0'}}%</small>
      </div>
    </div>

    <!-- Step Navigation -->
    <div class="modern-card mb-4">
      <div class="modern-card-body py-3">
        <div class="row g-2">
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.USER_REGISTRATION)"
              [disabled]="!canGoToStep(WizardStep.USER_REGISTRATION)"
              (click)="goToStep(WizardStep.USER_REGISTRATION)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.USER_REGISTRATION)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">1. Kullanıcı Kaydı</div>
                  <small class="text-muted">Temel bilgiler</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.COMPANY_INFO)"
              [disabled]="!canGoToStep(WizardStep.COMPANY_INFO)"
              (click)="goToStep(WizardStep.COMPANY_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.COMPANY_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">2. Şirket Bilgileri</div>
                  <small class="text-muted">Salon detayları</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.ADDRESS_INFO)"
              [disabled]="!canGoToStep(WizardStep.ADDRESS_INFO)"
              (click)="goToStep(WizardStep.ADDRESS_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.ADDRESS_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">3. Adres Bilgileri</div>
                  <small class="text-muted">Konum</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.OWNER_INFO)"
              [disabled]="!canGoToStep(WizardStep.OWNER_INFO)"
              (click)="goToStep(WizardStep.OWNER_INFO)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.OWNER_INFO)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">4. Salon Sahibi</div>
                  <small class="text-muted">İletişim</small>
                </div>
              </div>
            </button>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn w-100 text-start"
              [class]="getStepButtonClass(WizardStep.PREVIEW)"
              [disabled]="!canGoToStep(WizardStep.PREVIEW)"
              (click)="goToStep(WizardStep.PREVIEW)">
              <div class="d-flex align-items-center">
                <div class="me-2">
                  <fa-icon [icon]="getStepIcon(WizardStep.PREVIEW)"></fa-icon>
                </div>
                <div>
                  <div class="fw-bold">5. Önizleme</div>
                  <small class="text-muted">Kontrol & Kaydet</small>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container-fluid main-content">
  <div *ngIf="isSubmitting" class="d-flex justify-content-center align-items-center" style="height: 50vh;">
    <app-loading-spinner></app-loading-spinner>
  </div>

  <div class="fade-in" [class.content-blur]="isSubmitting">

    <!-- Wizard Content -->
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="modern-card">
          <div class="modern-card-body">

            <!-- Step 1: User Registration -->
            <div *ngIf="currentStep === WizardStep.USER_REGISTRATION" class="wizard-step">
              <form [formGroup]="userRegistrationForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <mat-form-field appearance="outline" class="modern-mat-form-field">
                        <mat-label>Ad</mat-label>
                        <input matInput formControlName="firstName" placeholder="Adınızı giriniz">
                        <mat-icon matPrefix>person</mat-icon>
                        <mat-error *ngIf="userRegistrationForm.get('firstName')?.hasError('required')">
                          Ad zorunludur
                        </mat-error>
                        <mat-error *ngIf="userRegistrationForm.get('firstName')?.hasError('minlength')">
                          Ad en az 2 karakter olmalıdır
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <mat-form-field appearance="outline" class="modern-mat-form-field">
                        <mat-label>Soyad</mat-label>
                        <input matInput formControlName="lastName" placeholder="Soyadınızı giriniz">
                        <mat-icon matPrefix>person</mat-icon>
                        <mat-error *ngIf="userRegistrationForm.get('lastName')?.hasError('required')">
                          Soyad zorunludur
                        </mat-error>
                        <mat-error *ngIf="userRegistrationForm.get('lastName')?.hasError('minlength')">
                          Soyad en az 2 karakter olmalıdır
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>E-posta</mat-label>
                    <input matInput formControlName="email" type="email" placeholder="E-posta adresinizi giriniz">
                    <mat-icon matPrefix>email</mat-icon>
                    <mat-error *ngIf="userRegistrationForm.get('email')?.hasError('required')">
                      E-posta zorunludur
                    </mat-error>
                    <mat-error *ngIf="userRegistrationForm.get('email')?.hasError('email')">
                      Geçerli bir e-posta adresi giriniz
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="alert alert-info">
                  <i class="fas fa-info-circle me-2"></i>
                  <strong>Bilgi:</strong> Şifreniz otomatik olarak telefon numaranızın son 4 hanesi olarak belirlenecektir. İlk girişinizde şifrenizi değiştirmeniz istenecektir.
                </div>
              </form>
            </div>

            <!-- Step 2: Company Info -->
            <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="wizard-step">
              <form [formGroup]="companyInfoForm">
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Şirket Adı</mat-label>
                    <input matInput formControlName="companyName" placeholder="Şirket adını giriniz">
                    <mat-icon matPrefix>business</mat-icon>
                    <mat-error *ngIf="companyInfoForm.get('companyName')?.hasError('required')">
                      Şirket adı zorunludur
                    </mat-error>
                    <mat-error *ngIf="companyInfoForm.get('companyName')?.hasError('minlength')">
                      Şirket adı en az 2 karakter olmalıdır
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Telefon Numarası</mat-label>
                    <input matInput formControlName="companyPhone" placeholder="0XXXXXXXXXX" maxlength="11">
                    <mat-icon matPrefix>phone</mat-icon>
                    <mat-error *ngIf="companyInfoForm.get('companyPhone')?.hasError('required')">
                      Telefon numarası zorunludur
                    </mat-error>
                    <mat-error *ngIf="companyInfoForm.get('companyPhone')?.hasError('pattern')">
                      Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                    </mat-error>
                  </mat-form-field>
                </div>
              </form>
            </div>

            <!-- Step 3: Address Info -->
            <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="wizard-step">
              <form [formGroup]="addressInfoForm">
                <div class="row">
                  <div class="col-md-6">
                    <div class="mb-3">
                      <mat-form-field appearance="outline" class="modern-mat-form-field">
                        <mat-label>İl</mat-label>
                        <mat-select formControlName="cityID" (selectionChange)="onCityChange()">
                          <mat-option *ngFor="let city of cities" [value]="city.cityID">
                            {{city.cityName}}
                          </mat-option>
                        </mat-select>
                        <mat-icon matPrefix>location_city</mat-icon>
                        <mat-error *ngIf="addressInfoForm.get('cityID')?.hasError('required')">
                          İl seçimi zorunludur
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="mb-3">
                      <mat-form-field appearance="outline" class="modern-mat-form-field">
                        <mat-label>İlçe</mat-label>
                        <mat-select formControlName="townID">
                          <mat-option *ngFor="let town of filteredTowns" [value]="town.townID">
                            {{town.townName}}
                          </mat-option>
                        </mat-select>
                        <mat-icon matPrefix>place</mat-icon>
                        <mat-error *ngIf="addressInfoForm.get('townID')?.hasError('required')">
                          İlçe seçimi zorunludur
                        </mat-error>
                      </mat-form-field>
                    </div>
                  </div>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Adres</mat-label>
                    <textarea matInput formControlName="address" placeholder="Detaylı adres bilgisini giriniz" rows="3"></textarea>
                    <mat-icon matPrefix>home</mat-icon>
                    <mat-error *ngIf="addressInfoForm.get('address')?.hasError('required')">
                      Adres zorunludur
                    </mat-error>
                    <mat-error *ngIf="addressInfoForm.get('address')?.hasError('minlength')">
                      Adres en az 10 karakter olmalıdır
                    </mat-error>
                  </mat-form-field>
                </div>
              </form>
            </div>

            <!-- Step 4: Owner Info -->
            <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="wizard-step">
              <form [formGroup]="ownerInfoForm">
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Salon Sahibi Ad Soyad</mat-label>
                    <input matInput formControlName="ownerName" placeholder="Salon sahibinin ad soyadını giriniz">
                    <mat-icon matPrefix>person</mat-icon>
                    <mat-error *ngIf="ownerInfoForm.get('ownerName')?.hasError('required')">
                      Salon sahibi adı zorunludur
                    </mat-error>
                    <mat-error *ngIf="ownerInfoForm.get('ownerName')?.hasError('minlength')">
                      Ad soyad en az 2 karakter olmalıdır
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="mb-3">
                  <mat-form-field appearance="outline" class="modern-mat-form-field">
                    <mat-label>Salon Sahibi Telefon</mat-label>
                    <input matInput formControlName="ownerPhone" placeholder="0XXXXXXXXXX" maxlength="11">
                    <mat-icon matPrefix>phone</mat-icon>
                    <mat-error *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('required')">
                      Telefon numarası zorunludur
                    </mat-error>
                    <mat-error *ngIf="ownerInfoForm.get('ownerPhone')?.hasError('pattern')">
                      Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                    </mat-error>
                  </mat-form-field>
                </div>
                <div class="alert alert-warning">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <strong>Önemli:</strong> Bu telefon numarasının son 4 hanesi, kullanıcının geçici şifresi olarak kullanılacaktır.
                </div>
              </form>
            </div>

            <!-- Step 5: Preview -->
            <div *ngIf="currentStep === WizardStep.PREVIEW" class="wizard-step">
              <div class="preview-summary">
                <h5 class="mb-4">
                  <i class="fas fa-eye me-2"></i>
                  Salon Bilgileri Önizlemesi
                </h5>

                <div class="row">
                  <div class="col-md-6">
                    <div class="preview-section">
                      <h6><i class="fas fa-user me-2"></i>Kullanıcı Bilgileri</h6>
                      <p><strong>Ad Soyad:</strong> {{wizardData.firstName}} {{wizardData.lastName}}</p>
                      <p><strong>E-posta:</strong> {{wizardData.email}}</p>
                    </div>

                    <div class="preview-section">
                      <h6><i class="fas fa-building me-2"></i>Şirket Bilgileri</h6>
                      <p><strong>Şirket Adı:</strong> {{wizardData.companyName}}</p>
                      <p><strong>Telefon:</strong> {{wizardData.companyPhone}}</p>
                    </div>
                  </div>

                  <div class="col-md-6">
                    <div class="preview-section">
                      <h6><i class="fas fa-map-marker-alt me-2"></i>Adres Bilgileri</h6>
                      <p><strong>İl/İlçe:</strong> {{wizardData.cityName}} / {{wizardData.townName}}</p>
                      <p><strong>Adres:</strong> {{wizardData.address}}</p>
                    </div>

                    <div class="preview-section">
                      <h6><i class="fas fa-user-tie me-2"></i>Salon Sahibi</h6>
                      <p><strong>Ad Soyad:</strong> {{wizardData.ownerName}}</p>
                      <p><strong>Telefon:</strong> {{wizardData.ownerPhone}}</p>
                      <p><strong>Geçici Şifre:</strong> {{wizardData.ownerPhone?.slice(-4)}} <small class="text-muted">(Son 4 hane)</small></p>
                    </div>
                  </div>
                </div>

                <div class="alert alert-success mt-4">
                  <i class="fas fa-check-circle me-2"></i>
                  <strong>Hazır!</strong> Tüm bilgiler doğru ise "Kaydet" butonuna tıklayarak salon kaydını tamamlayabilirsiniz.
                </div>
              </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="d-flex justify-content-between mt-4">
              <button
                type="button"
                class="modern-btn modern-btn-secondary"
                [disabled]="currentStep === WizardStep.USER_REGISTRATION"
                (click)="previousStep()">
                <fa-icon [icon]="faArrowLeft"></fa-icon>
                Önceki
              </button>

              <button
                *ngIf="currentStep < WizardStep.PREVIEW"
                type="button"
                class="modern-btn modern-btn-primary"
                [disabled]="!canProceedToNextStep()"
                (click)="nextStep()">
                Sonraki
                <fa-icon [icon]="faArrowRight"></fa-icon>
              </button>

              <button
                *ngIf="currentStep === WizardStep.PREVIEW"
                type="button"
                class="modern-btn modern-btn-success"
                [disabled]="isSubmitting || !validateAllForms()"
                (click)="saveCompanyWithOwner()">
                <fa-icon [icon]="faCheck"></fa-icon>
                {{ isSubmitting ? 'Kaydediliyor...' : 'Kaydet' }}
              </button>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4 mt-4">
      <div class="col-md-4">
        <div class="modern-stats-card bg-primary-light slide-in-left">
          <div class="modern-stats-icon bg-primary text-white">
            <i class="fas fa-building"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ salons.length }}</h2>
            <p class="modern-stats-label">Toplam Salon</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-success-light slide-in-left" style="animation-delay: 0.1s;">
          <div class="modern-stats-icon bg-success text-white">
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ getUniqueCityCount() }}</h2>
            <p class="modern-stats-label">Farklı Şehir</p>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="modern-stats-card bg-info-light slide-in-left" style="animation-delay: 0.2s;">
          <div class="modern-stats-icon bg-info text-white">
            <i class="fas fa-user-tie"></i>
          </div>
          <div class="modern-stats-info">
            <h2 class="modern-stats-value">{{ salons.length }}</h2>
            <p class="modern-stats-label">Salon Sahibi</p>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- Salon Listesi -->
      <div class="col-12">
        <div class="modern-card slide-in-right">
          <div class="modern-card-header">
            <div class="d-flex justify-content-between align-items-center">
              <h5><i class="fas fa-list me-2"></i>Salon Listesi</h5>
              <div class="position-relative">
                <input
                  type="text"
                  class="modern-form-control"
                  placeholder="Salon ara..."
                  [(ngModel)]="searchTerm"
                  (input)="filterSalons()">
                <i class="fas fa-search" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%);"></i>
              </div>
            </div>
          </div>
          <div class="modern-card-body">
            <div class="table-responsive">
              <table class="modern-table">
                <thead>
                  <tr>
                    <th>
                      <div class="d-flex align-items-center">
                        <span>Şirket Adı</span>
                        <i class="fas fa-sort ms-1" style="cursor: pointer;" (click)="sortSalons('companyName')"></i>
                      </div>
                    </th>
                    <th>Şirket Sahibi</th>
                    <th>İl/İlçe</th>
                    <th>İletişim</th>
                    <th>İşlem</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let salon of filteredSalons" class="fade-in">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="salon-icon me-2">
                          <i class="fas fa-building"></i>
                        </div>
                        {{ salon.companyName }}
                      </div>
                    </td>
                    <td>{{ salon.ownerName }}</td>
                    <td>
                      <span class="modern-badge modern-badge-info">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        {{ salon.cityName }}/{{ salon.townName }}
                      </span>
                    </td>
                    <td>
                      <div>{{ salon.ownerPhone }}</div>
                      <div class="text-muted small">{{ salon.ownerEmail }}</div>
                    </td>
                    <td>
                      <button class="modern-btn modern-btn-danger modern-btn-sm" (click)="deleteSalon(salon)" [disabled]="isSubmitting">
                        <i class="fas fa-trash-alt modern-btn-icon"></i>Sil
                      </button>
                    </td>
                  </tr>
                  <tr *ngIf="filteredSalons.length === 0">
                    <td colspan="5" class="text-center py-4">
                      <div class="text-muted">
                        <i class="fas fa-building fa-3x mb-3"></i>
                        <p>Henüz salon kaydı bulunmamaktadır.</p>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="modern-card-footer">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="modern-badge modern-badge-primary">Toplam: {{ filteredSalons.length }} salon</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .bg-primary-light {
    background-color: var(--primary-light);
  }
  
  .bg-success-light {
    background-color: var(--success-light);
  }
  
  .bg-info-light {
    background-color: var(--info-light);
  }
  
  .bg-primary {
    background-color: var(--primary);
  }
  
  .bg-success {
    background-color: var(--success);
  }
  
  .bg-info {
    background-color: var(--info);
  }
  
  .salon-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .content-blur {
    filter: blur(3px);
    pointer-events: none;
  }
  
  .form-section {
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
  }
  
  .form-section:hover {
    box-shadow: var(--shadow-sm);
    transform: translateY(-2px);
  }
  
  .form-section-title {
    color: var(--primary);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
  }
  
  .modern-mat-form-field {
    width: 100%;
  }
  
  /* Override Material styles for dark mode compatibility */
  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline .mat-form-field-outline {
    color: var(--border-color);
  }
  
  ::ng-deep .modern-mat-form-field.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
    color: var(--primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-form-field-label {
    color: var(--text-secondary);
  }
  
  ::ng-deep .modern-mat-form-field.mat-focused .mat-form-field-label {
    color: var(--primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-input-element {
    color: var(--text-primary);
  }
  
  ::ng-deep .modern-mat-form-field .mat-form-field-prefix {
    color: var(--primary);
    margin-right: 8px;
  }
</style>
