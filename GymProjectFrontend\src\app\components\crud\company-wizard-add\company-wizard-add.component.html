<!-- <PERSON><PERSON>er -->
<div class="sticky-header">
  <div class="modern-card">
    <div class="modern-card-header">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h4 class="mb-1">
            <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
            {{getStepTitle()}}
          </h4>
          <small class="text-muted">{{getStepDescription()}}</small>
        </div>
        <div class="d-flex gap-2">
          <button 
            type="button" 
            class="modern-btn modern-btn-outline-secondary"
            [disabled]="currentStep === WizardStep.USER_REGISTRATION"
            (click)="previousStep()">
            <fa-icon [icon]="faArrowLeft" class="me-1"></fa-icon>
            Geri
          </button>
          <button 
            type="button" 
            class="modern-btn modern-btn-primary"
            [disabled]="!canProceedToNextStep() || isSubmitting"
            (click)="nextStep()"
            *ngIf="currentStep < WizardStep.PREVIEW">
            İleri
            <fa-icon [icon]="faArrowRight" class="ms-1"></fa-icon>
          </button>
          <button 
            type="button" 
            class="modern-btn modern-btn-success"
            [disabled]="!isAllDataValid() || isSubmitting"
            (click)="submitWizard()"
            *ngIf="currentStep === WizardStep.PREVIEW">
            <fa-icon [icon]="faSave" class="me-1"></fa-icon>
            <span *ngIf="!isSubmitting">SALONU KAYDET</span>
            <span *ngIf="isSubmitting">Kaydediliyor...</span>
          </button>
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div class="mt-3">
        <div class="progress">
          <div 
            class="progress-bar bg-primary" 
            role="progressbar" 
            [style.width.%]="getProgressPercentage()">
          </div>
        </div>
      </div>
      
      <!-- Step Navigation -->
      <div class="d-flex justify-content-center mt-3 gap-2">
        <button 
          *ngFor="let step of [1,2,3,4,5]; let i = index"
          type="button"
          class="step-nav-btn"
          [class.active]="currentStep === step"
          [class.completed]="isStepCompleted(step)"
          [disabled]="!canGoToStep(step)"
          (click)="goToStep(step)">
          <fa-icon [icon]="getStepIcon(step)"></fa-icon>
          <span class="d-none d-md-inline ms-1">{{step}}</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="main-content">
  <div class="container-fluid">
    
    <!-- Step 1: User Registration -->
    <div *ngIf="currentStep === WizardStep.USER_REGISTRATION" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faUserPlus" class="me-2"></fa-icon>
          Kullanıcı Hesabı Oluştur
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="userRegistrationForm">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Ad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="userRegistrationForm.get('firstName')?.invalid && userRegistrationForm.get('firstName')?.touched"
                  formControlName="firstName"
                  placeholder="Adınızı giriniz">
                <div class="invalid-feedback" *ngIf="userRegistrationForm.get('firstName')?.invalid && userRegistrationForm.get('firstName')?.touched">
                  Ad alanı zorunludur ve en az 2 karakter olmalıdır
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Soyad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="userRegistrationForm.get('lastName')?.invalid && userRegistrationForm.get('lastName')?.touched"
                  formControlName="lastName"
                  placeholder="Soyadınızı giriniz">
                <div class="invalid-feedback" *ngIf="userRegistrationForm.get('lastName')?.invalid && userRegistrationForm.get('lastName')?.touched">
                  Soyad alanı zorunludur ve en az 2 karakter olmalıdır
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  E-posta <span class="text-danger">*</span>
                </label>
                <input
                  type="email"
                  class="modern-form-control"
                  [class.is-invalid]="userRegistrationForm.get('email')?.invalid && userRegistrationForm.get('email')?.touched"
                  formControlName="email"
                  placeholder="<EMAIL>">
                <div class="invalid-feedback" *ngIf="userRegistrationForm.get('email')?.invalid && userRegistrationForm.get('email')?.touched">
                  Geçerli bir e-posta adresi giriniz
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şifre <span class="text-danger">*</span>
                </label>
                <input
                  type="password"
                  class="modern-form-control"
                  [class.is-invalid]="userRegistrationForm.get('password')?.invalid && userRegistrationForm.get('password')?.touched"
                  formControlName="password"
                  placeholder="En az 6 karakter">
                <div class="invalid-feedback" *ngIf="userRegistrationForm.get('password')?.invalid && userRegistrationForm.get('password')?.touched">
                  Şifre en az 6 karakter olmalıdır
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 2: Company Information -->
    <div *ngIf="currentStep === WizardStep.COMPANY_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
          Şirket Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="companyInfoForm">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Adı <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="companyInfoForm.get('companyName')?.invalid && companyInfoForm.get('companyName')?.touched"
                  formControlName="companyName"
                  placeholder="Şirket adını giriniz">
                <div class="invalid-feedback" *ngIf="companyInfoForm.get('companyName')?.invalid && companyInfoForm.get('companyName')?.touched">
                  Şirket adı zorunludur
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Şirket Telefonu <span class="text-danger">*</span>
                </label>
                <input
                  type="tel"
                  class="modern-form-control"
                  [class.is-invalid]="companyInfoForm.get('companyPhone')?.invalid && companyInfoForm.get('companyPhone')?.touched"
                  formControlName="companyPhone"
                  placeholder="05xxxxxxxxx"
                  maxlength="11">
                <div class="invalid-feedback" *ngIf="companyInfoForm.get('companyPhone')?.invalid && companyInfoForm.get('companyPhone')?.touched">
                  Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 3: Address Information -->
    <div *ngIf="currentStep === WizardStep.ADDRESS_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
          Adres Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="addressInfoForm">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  İl <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="addressInfoForm.get('city')?.invalid && addressInfoForm.get('city')?.touched"
                  formControlName="city"
                  (change)="onCityChange()">
                  <option value="">İl seçiniz</option>
                  <option *ngFor="let city of cities" [ngValue]="city">{{city.cityName}}</option>
                </select>
                <div class="invalid-feedback" *ngIf="addressInfoForm.get('city')?.invalid && addressInfoForm.get('city')?.touched">
                  İl seçimi zorunludur
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  İlçe <span class="text-danger">*</span>
                </label>
                <select
                  class="modern-form-control"
                  [class.is-invalid]="addressInfoForm.get('town')?.invalid && addressInfoForm.get('town')?.touched"
                  formControlName="town"
                  [disabled]="!addressInfoForm.get('city')?.value">
                  <option value="">İlçe seçiniz</option>
                  <option *ngFor="let town of towns" [ngValue]="town">{{town.townName}}</option>
                </select>
                <div class="invalid-feedback" *ngIf="addressInfoForm.get('town')?.invalid && addressInfoForm.get('town')?.touched">
                  İlçe seçimi zorunludur
                </div>
              </div>
            </div>
            
            <div class="col-12">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Detay Adres <span class="text-danger">*</span>
                </label>
                <textarea
                  class="modern-form-control"
                  [class.is-invalid]="addressInfoForm.get('address')?.invalid && addressInfoForm.get('address')?.touched"
                  formControlName="address"
                  rows="3"
                  placeholder="Detaylı adres bilgisini giriniz"></textarea>
                <div class="invalid-feedback" *ngIf="addressInfoForm.get('address')?.invalid && addressInfoForm.get('address')?.touched">
                  Adres en az 10 karakter olmalıdır
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 4: Owner Information -->
    <div *ngIf="currentStep === WizardStep.OWNER_INFO" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faUser" class="me-2"></fa-icon>
          Salon Sahibi Bilgileri
        </h5>
      </div>
      <div class="modern-card-body">
        <form [formGroup]="ownerInfoForm">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Ad Soyad <span class="text-danger">*</span>
                </label>
                <input
                  type="text"
                  class="modern-form-control"
                  [class.is-invalid]="ownerInfoForm.get('ownerName')?.invalid && ownerInfoForm.get('ownerName')?.touched"
                  formControlName="ownerName"
                  placeholder="Ad soyadınızı giriniz">
                <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerName')?.invalid && ownerInfoForm.get('ownerName')?.touched">
                  Ad soyad zorunludur
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  E-posta <span class="text-danger">*</span>
                </label>
                <input
                  type="email"
                  class="modern-form-control"
                  [class.is-invalid]="ownerInfoForm.get('ownerEmail')?.invalid && ownerInfoForm.get('ownerEmail')?.touched"
                  formControlName="ownerEmail"
                  placeholder="E-posta adresiniz"
                  readonly>
                <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerEmail')?.invalid && ownerInfoForm.get('ownerEmail')?.touched">
                  Geçerli bir e-posta adresi giriniz
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="modern-form-group">
                <label class="modern-form-label">
                  Telefon Numarası <span class="text-danger">*</span>
                </label>
                <input
                  type="tel"
                  class="modern-form-control"
                  [class.is-invalid]="ownerInfoForm.get('ownerPhone')?.invalid && ownerInfoForm.get('ownerPhone')?.touched"
                  formControlName="ownerPhone"
                  placeholder="05xxxxxxxxx"
                  maxlength="11">
                <div class="invalid-feedback" *ngIf="ownerInfoForm.get('ownerPhone')?.invalid && ownerInfoForm.get('ownerPhone')?.touched">
                  Telefon numarası 0 ile başlamalı ve 11 haneli olmalıdır
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Step 5: Preview -->
    <div *ngIf="currentStep === WizardStep.PREVIEW" class="modern-card mb-4 fade-in">
      <div class="modern-card-header">
        <h5 class="mb-0">
          <fa-icon [icon]="faEye" class="me-2"></fa-icon>
          Bilgileri Gözden Geçirin
        </h5>
      </div>
      <div class="modern-card-body">
        <div class="row g-4">
          <!-- Kullanıcı Bilgileri -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faUserPlus" class="me-2"></fa-icon>
                Kullanıcı Bilgileri
              </h6>
              <div class="preview-item">
                <strong>Ad Soyad:</strong> {{wizardData.userRegistration?.firstName}} {{wizardData.userRegistration?.lastName}}
              </div>
              <div class="preview-item">
                <strong>E-posta:</strong> {{wizardData.userRegistration?.email}}
              </div>
            </div>
          </div>
          
          <!-- Şirket Bilgileri -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faBuilding" class="me-2"></fa-icon>
                Şirket Bilgileri
              </h6>
              <div class="preview-item">
                <strong>Şirket Adı:</strong> {{wizardData.companyInfo?.companyName}}
              </div>
              <div class="preview-item">
                <strong>Telefon:</strong> {{wizardData.companyInfo?.companyPhone}}
              </div>
            </div>
          </div>
          
          <!-- Adres Bilgileri -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faMapMarkerAlt" class="me-2"></fa-icon>
                Adres Bilgileri
              </h6>
              <div class="preview-item">
                <strong>İl/İlçe:</strong> {{wizardData.addressInfo?.city?.cityName}} / {{wizardData.addressInfo?.town?.townName}}
              </div>
              <div class="preview-item">
                <strong>Adres:</strong> {{wizardData.addressInfo?.address}}
              </div>
            </div>
          </div>
          
          <!-- Salon Sahibi Bilgileri -->
          <div class="col-md-6">
            <div class="preview-section">
              <h6 class="preview-section-title">
                <fa-icon [icon]="faUser" class="me-2"></fa-icon>
                Salon Sahibi Bilgileri
              </h6>
              <div class="preview-item">
                <strong>Ad Soyad:</strong> {{wizardData.ownerInfo?.ownerName}}
              </div>
              <div class="preview-item">
                <strong>Telefon:</strong> {{wizardData.ownerInfo?.ownerPhone}}
              </div>
            </div>
          </div>
        </div>
        
        <div class="alert alert-info mt-4">
          <fa-icon [icon]="faInfoCircle" class="me-2"></fa-icon>
          <strong>Bilgi:</strong> Salon sahibi için geçici şifre telefon numarasının son 4 hanesi olarak ayarlanacaktır. 
          İlk girişte şifre değiştirme zorunluluğu bulunmaktadır.
        </div>
      </div>
    </div>
    
  </div>
</div>
