import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { finalize } from 'rxjs/operators';

// Icons
import { 
  faArrowLeft, 
  faArrowRight, 
  faCheck, 
  faInfoCircle, 
  faBuilding, 
  faMapMarkerAlt, 
  faUser, 
  faEye,
  faSave,
  faUserPlus
} from '@fortawesome/free-solid-svg-icons';

// Services
import { CompanyService } from '../../../services/company.service';
import { AuthService } from '../../../services/auth.service';
import { CityService } from '../../../services/city.service';
import { TownService } from '../../../services/town.service';

// Models
import { City } from '../../../models/city';
import { Town } from '../../../models/town';

// Wizard adımları enum'u
enum WizardStep {
  USER_REGISTRATION = 1,
  COMPANY_INFO = 2,
  ADDRESS_INFO = 3,
  OWNER_INFO = 4,
  PREVIEW = 5
}

@Component({
  selector: 'app-company-wizard-add',
  templateUrl: './company-wizard-add.component.html',
  styleUrls: ['./company-wizard-add.component.css'],
  standalone: false
})
export class CompanyWizardAddComponent implements OnInit {
  // Icons
  faArrowLeft = faArrowLeft;
  faArrowRight = faArrowRight;
  faCheck = faCheck;
  faInfoCircle = faInfoCircle;
  faBuilding = faBuilding;
  faMapMarkerAlt = faMapMarkerAlt;
  faUser = faUser;
  faEye = faEye;
  faSave = faSave;
  faUserPlus = faUserPlus;

  // Wizard state
  currentStep: WizardStep = WizardStep.USER_REGISTRATION;
  WizardStep = WizardStep; // Template'de kullanmak için

  // Forms
  userRegistrationForm!: FormGroup;
  companyInfoForm!: FormGroup;
  addressInfoForm!: FormGroup;
  ownerInfoForm!: FormGroup;
  
  // Loading states
  isSubmitting = false;
  isLoadingCities = false;
  isLoadingTowns = false;

  // Data
  cities: City[] = [];
  towns: Town[] = [];
  completedSteps: Set<WizardStep> = new Set();
  
  // Wizard data storage
  wizardData: any = {};
  registeredUserEmail: string = '';

  constructor(
    private fb: FormBuilder,
    private companyService: CompanyService,
    private authService: AuthService,
    private cityService: CityService,
    private townService: TownService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.loadCities();
  }

  initializeForms(): void {
    // Adım 1: Kullanıcı kayıt formu
    this.userRegistrationForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    // Adım 2: Şirket bilgileri formu
    this.companyInfoForm = this.fb.group({
      companyName: ['', [Validators.required, Validators.minLength(2)]],
      companyPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });

    // Adım 3: Adres bilgileri formu
    this.addressInfoForm = this.fb.group({
      city: ['', Validators.required],
      town: ['', Validators.required],
      address: ['', [Validators.required, Validators.minLength(10)]]
    });

    // Adım 4: Salon sahibi bilgileri formu
    this.ownerInfoForm = this.fb.group({
      ownerName: ['', [Validators.required, Validators.minLength(2)]],
      ownerEmail: ['', [Validators.required, Validators.email]],
      ownerPhone: ['', [Validators.required, Validators.pattern(/^0[0-9]{10}$/)]]
    });
  }

  // Wizard navigation methods
  nextStep(): void {
    if (this.canProceedToNextStep()) {
      // Mevcut adımı tamamlandı olarak işaretle
      this.completedSteps.add(this.currentStep);

      if (this.currentStep < WizardStep.PREVIEW) {
        this.currentStep++;
        this.onStepChange();
      }
    } else {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.validateAndHighlightErrors();
    }
  }

  previousStep(): void {
    if (this.currentStep > WizardStep.USER_REGISTRATION) {
      this.currentStep--;
    }
  }

  goToStep(step: WizardStep): void {
    if (this.canGoToStep(step)) {
      this.currentStep = step;
      this.onStepChange();
    }
  }

  canGoToStep(step: WizardStep): boolean {
    // Sadece tamamlanmış adımlara veya bir sonraki adıma gidebilir
    return step <= this.currentStep || this.completedSteps.has(step - 1);
  }

  isStepCompleted(step: WizardStep): boolean {
    return this.completedSteps.has(step);
  }

  onStepChange(): void {
    switch (this.currentStep) {
      case WizardStep.COMPANY_INFO:
        this.saveUserRegistrationInfo();
        break;
      case WizardStep.ADDRESS_INFO:
        this.saveCompanyInfo();
        break;
      case WizardStep.OWNER_INFO:
        this.saveAddressInfo();
        // Kayıtlı email'i otomatik doldur
        if (this.registeredUserEmail) {
          this.ownerInfoForm.patchValue({
            ownerEmail: this.registeredUserEmail
          });
        }
        break;
      case WizardStep.PREVIEW:
        this.saveOwnerInfo();
        this.preparePreviewData();
        break;
    }
  }

  // Step validation methods
  canProceedToNextStep(): boolean {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return this.userRegistrationForm.valid;
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm.valid;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm.valid;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm.valid;
      default:
        return true;
    }
  }

  // Data saving methods
  saveUserRegistrationInfo(): void {
    if (this.userRegistrationForm.valid) {
      this.wizardData.userRegistration = this.userRegistrationForm.value;
      
      // Kullanıcı kaydını gerçekleştir
      this.registerUser();
    }
  }

  saveCompanyInfo(): void {
    if (this.companyInfoForm.valid) {
      this.wizardData.companyInfo = this.companyInfoForm.value;
    }
  }

  saveAddressInfo(): void {
    if (this.addressInfoForm.valid) {
      this.wizardData.addressInfo = this.addressInfoForm.value;
    }
  }

  saveOwnerInfo(): void {
    if (this.ownerInfoForm.valid) {
      this.wizardData.ownerInfo = this.ownerInfoForm.value;
    }
  }

  preparePreviewData(): void {
    // Tüm verileri birleştir
    this.wizardData.preview = {
      ...this.wizardData.userRegistration,
      ...this.wizardData.companyInfo,
      ...this.wizardData.addressInfo,
      ...this.wizardData.ownerInfo
    };
  }

  // User registration
  registerUser(): void {
    if (!this.userRegistrationForm.valid) return;

    this.isSubmitting = true;
    const registerModel = this.userRegistrationForm.value;

    this.authService.register(registerModel)
      .pipe(finalize(() => this.isSubmitting = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.registeredUserEmail = registerModel.email;
            this.toastrService.success('Kullanıcı kaydı başarılı!', 'Başarılı');
          } else {
            this.toastrService.error(response.message || 'Kullanıcı kaydı başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Kullanıcı kaydı sırasında hata oluştu');
        }
      });
  }

  // Cities and Towns
  loadCities(): void {
    this.isLoadingCities = true;
    this.cityService.getCities()
      .pipe(finalize(() => this.isLoadingCities = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.cities = response.data;
          }
        },
        error: (error) => {
          this.toastrService.error('Şehirler yüklenemedi');
        }
      });
  }

  onCityChange(): void {
    const selectedCity = this.addressInfoForm.get('city')?.value;
    if (selectedCity) {
      this.loadTowns(selectedCity.cityID);
      // İlçe seçimini sıfırla
      this.addressInfoForm.patchValue({ town: '' });
    }
  }

  loadTowns(cityId: number): void {
    this.isLoadingTowns = true;
    this.townService.getTownsByCityId(cityId)
      .pipe(finalize(() => this.isLoadingTowns = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.towns = response.data;
          }
        },
        error: (error) => {
          this.toastrService.error('İlçeler yüklenemedi');
        }
      });
  }

  // Final submission
  submitWizard(): void {
    if (!this.isAllDataValid()) {
      this.toastrService.error('Lütfen tüm bilgileri kontrol ediniz');
      return;
    }

    this.isSubmitting = true;
    
    // CompanyWithOwnerDto formatında veri hazırla
    const companyWithOwnerDto = {
      // User bilgileri
      firstName: this.wizardData.userRegistration.firstName,
      lastName: this.wizardData.userRegistration.lastName,
      email: this.wizardData.userRegistration.email,
      password: this.generateTempPassword(this.wizardData.ownerInfo.ownerPhone),
      
      // Şirket bilgileri
      companyName: this.wizardData.companyInfo.companyName,
      companyPhone: this.wizardData.companyInfo.companyPhone,
      
      // Adres bilgileri
      cityID: this.wizardData.addressInfo.city.cityID,
      townID: this.wizardData.addressInfo.town.townID,
      address: this.wizardData.addressInfo.address,
      
      // Salon sahibi bilgileri
      ownerName: this.wizardData.ownerInfo.ownerName,
      ownerPhone: this.wizardData.ownerInfo.ownerPhone
    };

    this.companyService.addCompanyWithOwner(companyWithOwnerDto)
      .pipe(finalize(() => this.isSubmitting = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Salon başarıyla oluşturuldu!', 'Başarılı');
            this.router.navigate(['/company/unified-add']);
          } else {
            this.toastrService.error(response.message || 'Salon oluşturulamadı');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Salon oluşturulurken hata oluştu');
        }
      });
  }

  // Helper methods
  generateTempPassword(phoneNumber: string): string {
    // Telefon numarasının son 4 hanesi
    return phoneNumber.slice(-4);
  }

  isAllDataValid(): boolean {
    return this.userRegistrationForm.valid &&
           this.companyInfoForm.valid &&
           this.addressInfoForm.valid &&
           this.ownerInfoForm.valid;
  }

  validateAndHighlightErrors(): void {
    // Form validation ve error highlighting
    const currentForm = this.getCurrentForm();
    if (currentForm) {
      Object.keys(currentForm.controls).forEach(key => {
        const control = currentForm.get(key);
        if (control && control.invalid) {
          control.markAsTouched();
        }
      });
    }
  }

  getCurrentForm(): FormGroup | null {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return this.userRegistrationForm;
      case WizardStep.COMPANY_INFO:
        return this.companyInfoForm;
      case WizardStep.ADDRESS_INFO:
        return this.addressInfoForm;
      case WizardStep.OWNER_INFO:
        return this.ownerInfoForm;
      default:
        return null;
    }
  }

  // Step helper methods
  getStepTitle(): string {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return 'Kullanıcı Kaydı';
      case WizardStep.COMPANY_INFO:
        return 'Şirket Bilgileri';
      case WizardStep.ADDRESS_INFO:
        return 'Adres Bilgileri';
      case WizardStep.OWNER_INFO:
        return 'Salon Sahibi Bilgileri';
      case WizardStep.PREVIEW:
        return 'Önizleme ve Kaydet';
      default:
        return '';
    }
  }

  getStepDescription(): string {
    switch (this.currentStep) {
      case WizardStep.USER_REGISTRATION:
        return 'Salon sahibi hesabı oluşturun';
      case WizardStep.COMPANY_INFO:
        return 'Şirket adı ve telefon bilgilerini girin';
      case WizardStep.ADDRESS_INFO:
        return 'Salon adres bilgilerini girin';
      case WizardStep.OWNER_INFO:
        return 'Salon sahibi iletişim bilgilerini girin';
      case WizardStep.PREVIEW:
        return 'Bilgileri gözden geçirin ve salonu kaydedin';
      default:
        return '';
    }
  }

  getStepIcon(step: WizardStep): any {
    if (this.isStepCompleted(step)) {
      return this.faCheck;
    }

    switch (step) {
      case WizardStep.USER_REGISTRATION:
        return this.faUserPlus;
      case WizardStep.COMPANY_INFO:
        return this.faBuilding;
      case WizardStep.ADDRESS_INFO:
        return this.faMapMarkerAlt;
      case WizardStep.OWNER_INFO:
        return this.faUser;
      case WizardStep.PREVIEW:
        return this.faEye;
      default:
        return this.faInfoCircle;
    }
  }

  getProgressPercentage(): number {
    return (this.currentStep / WizardStep.PREVIEW) * 100;
  }
}
