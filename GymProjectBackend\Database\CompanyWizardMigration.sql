-- Company Wizard Migration Script
-- Bu script, company wizard siste<PERSON> için gerekli veritabanı değişikliklerini içerir

-- 1. Users tablosuna RequirePasswordChange kolonu ekleme (eğer yoksa)
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'RequirePasswordChange')
BEGIN
    ALTER TABLE [dbo].[Users] 
    ADD RequirePasswordChange BIT NOT NULL DEFAULT 0;
    
    PRINT 'RequirePasswordChange kolonu Users tablosuna eklendi.';
END
ELSE
BEGIN
    PRINT 'RequirePasswordChange kolonu zaten mevcut.';
END

-- 2. Mevcut verileri güncelleme (opsiyonel)
-- Eğer mevcut kullanıcıların şifre değiştirme zorunluluğu olmasını istiyorsanız:
-- UPDATE [dbo].[Users] SET RequirePasswordChange = 1 WHERE RequirePasswordChange IS NULL;

-- 3. Index'leri kontrol etme ve oluşturma
-- Email için unique index (eğer yoksa)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Users]') AND name = 'IX_Users_Email')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_Users_Email] ON [dbo].[Users] ([Email])
    WHERE [Email] IS NOT NULL;
    
    PRINT 'Email için unique index oluşturuldu.';
END
ELSE
BEGIN
    PRINT 'Email unique index zaten mevcut.';
END

-- 4. Company tablosu için index'ler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Companies]') AND name = 'IX_Companies_IsActive')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Companies_IsActive] ON [dbo].[Companies] ([IsActive]);
    
    PRINT 'Companies IsActive index oluşturuldu.';
END

-- 5. UserCompany tablosu için composite index
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[UserCompanies]') AND name = 'IX_UserCompanies_UserID_CompanyID')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [IX_UserCompanies_UserID_CompanyID] 
    ON [dbo].[UserCompanies] ([UserID], [CompanyID])
    WHERE [IsActive] = 1;
    
    PRINT 'UserCompanies composite index oluşturuldu.';
END

-- 6. Performance için ek index'ler
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CompanyAdresses]') AND name = 'IX_CompanyAdresses_CompanyID')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_CompanyAdresses_CompanyID] ON [dbo].[CompanyAdresses] ([CompanyID]);
    
    PRINT 'CompanyAdresses CompanyID index oluşturuldu.';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[CompanyUsers]') AND name = 'IX_CompanyUsers_Email')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_CompanyUsers_Email] ON [dbo].[CompanyUsers] ([Email]);
    
    PRINT 'CompanyUsers Email index oluşturuldu.';
END

-- 7. Stored Procedure oluşturma (Transaction-based company creation için)
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CreateCompanyWithOwner]') AND type in (N'P', N'PC'))
BEGIN
    DROP PROCEDURE [dbo].[sp_CreateCompanyWithOwner];
END

CREATE PROCEDURE [dbo].[sp_CreateCompanyWithOwner]
    @FirstName NVARCHAR(50),
    @LastName NVARCHAR(50),
    @Email NVARCHAR(100),
    @PasswordHash VARBINARY(500),
    @PasswordSalt VARBINARY(500),
    @CompanyName NVARCHAR(100),
    @CompanyPhone NVARCHAR(15),
    @CityID INT,
    @TownID INT,
    @Address NVARCHAR(500),
    @OwnerName NVARCHAR(100),
    @OwnerPhone NVARCHAR(15)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserID INT, @CompanyID INT;
    
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- 1. User oluştur
        INSERT INTO [dbo].[Users] (FirstName, LastName, Email, PasswordHash, PasswordSalt, IsActive, RequirePasswordChange, CreationDate)
        VALUES (@FirstName, @LastName, @Email, @PasswordHash, @PasswordSalt, 1, 1, GETDATE());
        
        SET @UserID = SCOPE_IDENTITY();
        
        -- 2. Company oluştur
        INSERT INTO [dbo].[Companies] (CompanyName, PhoneNumber, IsActive, CreationDate)
        VALUES (@CompanyName, @CompanyPhone, 1, GETDATE());
        
        SET @CompanyID = SCOPE_IDENTITY();
        
        -- 3. Company Address oluştur
        INSERT INTO [dbo].[CompanyAdresses] (CompanyID, CityID, TownID, Adress, IsActive, CreationDate)
        VALUES (@CompanyID, @CityID, @TownID, @Address, 1, GETDATE());
        
        -- 4. Company User oluştur
        INSERT INTO [dbo].[CompanyUsers] (CityID, TownID, Name, PhoneNumber, Email, IsActive, CreationDate)
        VALUES (@CityID, @TownID, @OwnerName, @OwnerPhone, @Email, 1, GETDATE());
        
        -- 5. User-Company ilişkisi oluştur
        INSERT INTO [dbo].[UserCompanies] (UserID, CompanyID, IsActive, CreationDate)
        VALUES (@UserID, @CompanyID, 1, GETDATE());
        
        COMMIT TRANSACTION;
        
        SELECT 'SUCCESS' AS Result, @CompanyID AS CompanyID, @UserID AS UserID;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        
    END CATCH
END

PRINT 'sp_CreateCompanyWithOwner stored procedure oluşturuldu.';

-- 8. Trigger oluşturma (Audit için)
IF EXISTS (SELECT * FROM sys.triggers WHERE object_id = OBJECT_ID(N'[dbo].[tr_Companies_Audit]'))
BEGIN
    DROP TRIGGER [dbo].[tr_Companies_Audit];
END

CREATE TRIGGER [dbo].[tr_Companies_Audit]
ON [dbo].[Companies]
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Basit audit log (opsiyonel)
    IF EXISTS (SELECT * FROM inserted)
    BEGIN
        INSERT INTO [dbo].[AuditLog] (TableName, Operation, RecordID, ChangeDate, UserID)
        SELECT 'Companies', 
               CASE WHEN EXISTS (SELECT * FROM deleted) THEN 'UPDATE' ELSE 'INSERT' END,
               CompanyID, 
               GETDATE(), 
               SYSTEM_USER
        FROM inserted;
    END
    
    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS (SELECT * FROM inserted)
    BEGIN
        INSERT INTO [dbo].[AuditLog] (TableName, Operation, RecordID, ChangeDate, UserID)
        SELECT 'Companies', 'DELETE', CompanyID, GETDATE(), SYSTEM_USER
        FROM deleted;
    END
END

PRINT 'Companies audit trigger oluşturuldu.';

PRINT 'Company Wizard Migration tamamlandı!';
