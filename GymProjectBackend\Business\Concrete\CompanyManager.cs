﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using Core.Entities.Concrete;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyManager : ICompanyService
    {
        ICompanyDal _companyDal;
        IUserService _userService;
        ICompanyAdressService _companyAdressService;
        ICompanyUserService _companyUserService;
        IUserCompanyService _userCompanyService;

        public CompanyManager(ICompanyDal companyDal, IUserService userService,
            ICompanyAdressService companyAdressService, ICompanyUserService companyUserService,
            IUserCompanyService userCompanyService)
        {
            _companyDal = companyDal;
            _userService = userService;
            _companyAdressService = companyAdressService;
            _companyUserService = companyUserService;
            _userCompanyService = userCompanyService;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Company")]
        [ValidationAspect(typeof(CompanyValidator))]
        public IResult Add(Company company)
        {
            _companyDal.Add(company);
            return new SuccessResult(Messages.CompanyAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("Company")]
        public IResult Delete(int id)
        {
            _companyDal.Delete(id);
            return new SuccessResult(Messages.CompanyDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "Company", "Active")]
        public IDataResult<List<ActiveCompanyDetailDto>> GetActiveCompanies()
        {
            return new SuccessDataResult<List<ActiveCompanyDetailDto>>(_companyDal.GetActiveCompanies());
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "Company", "Master")]
        public IDataResult<List<Company>> GetAll()
        {
            return new SuccessDataResult<List<Company>>(_companyDal.GetAll());
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("Company")]
        [ValidationAspect(typeof(CompanyValidator))]
        public IResult Update(Company company)
        {
            _companyDal.Update(company);
            return new SuccessResult(Messages.CompanyUpdated);
        }

        [LogAspect]
        [PerformanceAspect(5)]
        [TransactionScopeAspect]
        public IResult AddCompanyWithOwner(CompanyWithOwnerDto companyWithOwnerDto)
        {
            try
            {
                // 1. Önce kullanıcıyı oluştur
                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(companyWithOwnerDto.Password, out passwordHash, out passwordSalt);

                var user = new User
                {
                    FirstName = companyWithOwnerDto.FirstName,
                    LastName = companyWithOwnerDto.LastName,
                    Email = companyWithOwnerDto.Email,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = true, // Telefon numarası şifresi olduğu için değiştirme zorunluluğu
                    CreationDate = DateTime.Now
                };

                var userResult = _userService.Add(user);
                if (!userResult.Success)
                {
                    return new ErrorResult("Kullanıcı oluşturulamadı: " + userResult.Message);
                }

                // Kullanıcı ID'sini al
                var createdUser = _userService.GetByMail(companyWithOwnerDto.Email);
                if (createdUser == null)
                {
                    return new ErrorResult("Oluşturulan kullanıcı bulunamadı");
                }

                // 2. Şirketi oluştur
                var company = new Company
                {
                    CompanyName = companyWithOwnerDto.CompanyName,
                    PhoneNumber = companyWithOwnerDto.CompanyPhone,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _companyDal.Add(company);

                // Şirket ID'sini al (en son eklenen şirket)
                var companies = _companyDal.GetAll();
                var createdCompany = companies.OrderByDescending(c => c.CreationDate).FirstOrDefault();
                if (createdCompany == null)
                {
                    return new ErrorResult("Şirket oluşturulamadı");
                }

                // 3. Şirket adresini oluştur
                var companyAddress = new CompanyAdress
                {
                    CompanyID = createdCompany.CompanyID,
                    CityID = companyWithOwnerDto.CityID,
                    TownID = companyWithOwnerDto.TownID,
                    Adress = companyWithOwnerDto.Address,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var addressResult = _companyAdressService.Add(companyAddress);
                if (!addressResult.Success)
                {
                    return new ErrorResult("Şirket adresi oluşturulamadı: " + addressResult.Message);
                }

                // 4. Şirket sahibini oluştur
                var companyUser = new CompanyUser
                {
                    CityID = companyWithOwnerDto.CityID,
                    TownID = companyWithOwnerDto.TownID,
                    Name = companyWithOwnerDto.OwnerName,
                    PhoneNumber = companyWithOwnerDto.OwnerPhone,
                    Email = companyWithOwnerDto.Email,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var companyUserResult = _companyUserService.Add(companyUser);
                if (!companyUserResult.Success)
                {
                    return new ErrorResult("Şirket sahibi oluşturulamadı: " + companyUserResult.Message);
                }

                // 5. User-Company ilişkisini oluştur
                var userCompany = new UserCompany
                {
                    UserID = createdUser.UserID,
                    CompanyId = createdCompany.CompanyID,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                var userCompanyResult = _userCompanyService.Add(userCompany);
                if (!userCompanyResult.Success)
                {
                    return new ErrorResult("Kullanıcı-şirket ilişkisi oluşturulamadı: " + userCompanyResult.Message);
                }

                return new SuccessResult("Salon başarıyla oluşturuldu");
            }
            catch (Exception ex)
            {
                return new ErrorResult("Salon oluşturulurken hata oluştu: " + ex.Message);
            }
        }
    }
}
