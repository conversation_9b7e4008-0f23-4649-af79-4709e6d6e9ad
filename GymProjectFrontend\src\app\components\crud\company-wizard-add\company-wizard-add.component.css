/* Company Wizard Add Styles */

/* <PERSON><PERSON> Header */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 1020;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) var(--transition-timing);
}

[data-theme="dark"] .sticky-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding-top: 0;
}

/* Progress Bar Enhancements */
.progress {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-pill);
}

.progress-bar {
  transition: width 0.6s ease;
  border-radius: var(--border-radius-pill);
}

/* Step Navigation Buttons */
.step-nav-btn {
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 0.75rem 1rem;
  color: var(--text-secondary);
  transition: all var(--transition-speed) var(--transition-timing);
  cursor: pointer;
  min-width: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.step-nav-btn:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.step-nav-btn.active {
  background: var(--primary);
  border-color: var(--primary);
  color: white;
  box-shadow: var(--shadow-md);
}

.step-nav-btn.completed {
  background: var(--success);
  border-color: var(--success);
  color: white;
}

.step-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form Enhancements */
.modern-form-group {
  margin-bottom: 1.5rem;
}

.modern-form-label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  display: block;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-speed) var(--transition-timing);
  font-size: 1rem;
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.modern-form-control.is-invalid {
  border-color: var(--danger);
}

.modern-form-control.is-invalid:focus {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem rgba(var(--danger-rgb), 0.25);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

/* Preview Sections */
.preview-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
}

.preview-section-title {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.preview-item {
  margin-bottom: 0.75rem;
  padding: 0.5rem 0;
}

.preview-item strong {
  color: var(--text-primary);
  display: inline-block;
  min-width: 100px;
}

/* Button Enhancements */
.modern-btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-lg);
  font-weight: 600;
  transition: all var(--transition-speed) var(--transition-timing);
  border: 2px solid transparent;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.modern-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.modern-btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.modern-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.modern-btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.modern-btn-success:hover:not(:disabled) {
  background-color: var(--success-dark);
  border-color: var(--success-dark);
}

.modern-btn-outline-secondary {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.modern-btn-outline-secondary:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border-color: var(--primary);
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sticky-header .modern-card-header {
    padding: 0.75rem 1rem;
  }

  .sticky-header .modern-card-header .d-flex.gap-2 {
    flex-direction: row;
    gap: 0.25rem !important;
  }

  .sticky-header .modern-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }

  .sticky-header h4 {
    font-size: 1.1rem;
  }

  .sticky-header small {
    font-size: 0.75rem;
  }

  .step-nav-btn {
    padding: 0.5rem;
    min-width: 45px;
  }

  .step-nav-btn span {
    display: none !important;
  }

  .modern-form-control {
    padding: 0.625rem 0.875rem;
  }

  .preview-section {
    padding: 1rem;
  }

  .preview-item strong {
    min-width: 80px;
    font-size: 0.875rem;
  }
}

/* Dark Mode Specific Adjustments */
[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-form-control:focus {
  background-color: var(--bg-primary);
  border-color: var(--primary);
}

[data-theme="dark"] .preview-section {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .step-nav-btn {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

[data-theme="dark"] .step-nav-btn:hover:not(:disabled) {
  background-color: var(--bg-secondary);
  border-color: var(--primary);
  color: var(--primary);
}

[data-theme="dark"] .step-nav-btn.completed {
  background-color: var(--success);
  border-color: var(--success);
  color: white;
}

[data-theme="dark"] .alert-info {
  background-color: var(--info-light);
  border-color: rgba(var(--info-rgb), 0.3);
  color: var(--info);
}

/* Loading States */
.modern-btn[disabled] {
  position: relative;
}

.modern-btn[disabled]::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Form Validation Enhancements */
.modern-form-control:valid {
  border-color: var(--success);
}

.modern-form-control:valid:focus {
  border-color: var(--success);
  box-shadow: 0 0 0 0.2rem rgba(var(--success-rgb), 0.25);
}

/* Utility Classes */
.text-danger {
  color: var(--danger) !important;
}

.text-success {
  color: var(--success) !important;
}

.text-primary {
  color: var(--primary) !important;
}

.text-muted {
  color: var(--text-muted) !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}
